# Protocol转换器使用说明

## 问题背景

在使用MapStruct转换Protocol生成的类时，经常需要添加大量的`@Mapping(target = "xxx", ignore = true)`注解来忽略Protocol生成的无效字段，如：

```java
@Mapping(target = "unknownFields", ignore = true)
@Mapping(target = "propIdBytes", ignore = true)
@Mapping(target = "mergeFrom", ignore = true)
@Mapping(target = "defaultInstanceForType", ignore = true)
@Mapping(target = "allFields", ignore = true)
@Mapping(target = "mergeUnknownFields", ignore = true)
@Mapping(target = "clearField", ignore = true)
// ... 更多重复的ignore注解
```

## 解决方案

创建了`ProtocolConvert`基础转换器，提供预定义的注解来批量处理Protocol字段的忽略。

## 使用方法

### 1. 在Mapper中引入ProtocolConvert

```java
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {GamePropGrantStatus.class, Date.class}, 
        uses = {CommonConvert.class, ProtocolConvert.class}  // 添加ProtocolConvert
)
public interface GamePropServiceConvert {
    // ...
}
```

### 2. 使用预定义的注解

#### 基础Protocol字段忽略
```java
@ProtocolConvert.ProtocolMapping
GamePropServiceProto.GameProp convertToProto(GamePropBean bean);
```

#### 包含字符串字段的Protocol类
```java
@ProtocolConvert.ProtocolStringMapping
GamePropServiceProto.GameProp convertToProto(GamePropBean bean);
```

#### 包含数字字段的Protocol类
```java
@ProtocolConvert.ProtocolNumberMapping
GamePropServiceProto.GameProp convertToProto(GamePropBean bean);
```

#### 最完整的Protocol字段忽略（推荐）
```java
@ProtocolConvert.ProtocolCompleteMapping
GamePropServiceProto.GameProp convertToProto(GamePropBean bean);
```

## 注解说明

### @ProtocolMapping
忽略Protocol生成类的基础无效字段：
- unknownFields
- defaultInstanceForType
- allFields
- mergeFrom
- mergeUnknownFields
- clearField
- serializedSize
- parserForType
- descriptorForType
- initializationErrorString
- initialized

### @ProtocolStringMapping
继承`@ProtocolMapping`，额外忽略字符串相关的Bytes字段：
- channelPropIdBytes
- channelGameIdBytes
- nameBytes
- propDescBytes
- operatorBytes
- remarkBytes
- iconUrlBytes
- propIdBytes
- eventNameBytes
- channelBytes
- appIdBytes
- gameIdBytes
- dataJsonBytes
- roomIdBytes
- msgBytes
- dataBytes
- userIdBytes
- uniqueIdBytes

### @ProtocolNumberMapping
继承`@ProtocolMapping`，额外忽略数字相关字段：
- memoizedHashCode
- memoizedSize

### @ProtocolCompleteMapping
继承`@ProtocolStringMapping`和`@ProtocolNumberMapping`，提供最完整的字段忽略。

## 迁移示例

### 迁移前
```java
@Mapping(target = "channelPropIdBytes", ignore = true)
@Mapping(target = "channelGameIdBytes", ignore = true)
@Mapping(target = "unknownFields", ignore = true)
@Mapping(target = "remarkBytes", ignore = true)
@Mapping(target = "propDescBytes", ignore = true)
@Mapping(target = "operatorBytes", ignore = true)
@Mapping(target = "nameBytes", ignore = true)
@Mapping(target = "mergeFrom", ignore = true)
@Mapping(target = "iconUrlBytes", ignore = true)
@Mapping(target = "defaultInstanceForType", ignore = true)
@Mapping(target = "allFields", ignore = true)
@Mapping(target = "mergeUnknownFields", ignore = true)
@Mapping(target = "clearField", ignore = true)
GamePropServiceProto.GameProp convertGamePropBeanToGameProp(GamePropBean gamePropBean);
```

### 迁移后
```java
@ProtocolConvert.ProtocolCompleteMapping
GamePropServiceProto.GameProp convertGamePropBeanToGameProp(GamePropBean gamePropBean);
```

## 优势

1. **代码简洁**：一个注解替代十几个ignore映射
2. **维护性好**：集中管理Protocol字段忽略规则
3. **可复用**：其他Protocol转换器也能使用
4. **类型安全**：保持MapStruct的类型检查
5. **扩展性好**：可以根据需要添加新的字段忽略规则

## 注意事项

1. 如果Protocol类有特殊字段需要映射，可以在使用注解的同时添加额外的`@Mapping`注解
2. 建议优先使用`@ProtocolCompleteMapping`，除非确定不需要某些字段的忽略
3. 如果发现新的Protocol无效字段，可以在`ProtocolConvert`中添加相应的忽略规则
