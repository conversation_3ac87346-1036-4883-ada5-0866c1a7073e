package fm.lizhi.ocean.seal.api;

import fm.lizhi.commons.service.client.annotation.Attribute;
import fm.lizhi.commons.service.client.annotation.Return;
import fm.lizhi.commons.service.client.annotation.Service;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.*;

/**
 * this file generated by autoapi-maven-plugin
 * 
 * do not edit this file manually, because this file is covered when you run autoapi-maven-plugin
 * 
 */

public interface GamePropService {
	
	
	/**
	 *  发放游戏道具/皮肤
	 *
	 * @param param
	 *            
	 * @return 
	 *     //if rcode == 0 发放成功<br>
	 *     //if rcode == 1 参数错误<br>
	 *     //if rcode == 2 道具不存在<br>
	 *     //if rcode == 3 用户不存在<br>
	 *     //if rcode == 4 发放失败<br>
	 *     //if rcode == 5 内部错误<br>
	 */
	@Service(domain = 4302, op = 260, request = RequestGrantGameProp.class, response = ResponseGrantGameProp.class)
	@Return(resultType = ResponseGrantGameProp.class)
	Result<ResponseGrantGameProp> grantGameProp(@Attribute(name = "param") GrantGamePropParam param);
	
	
	/**
	 *  获取道具列表
	 *
	 * @param param
	 *            
	 * @return 
	 *     //if rcode == 0 查询成功<br>
	 *     //if rcode == 1 参数错误<br>
	 *     //if rcode == 2 内部错误<br>
	 */
	@Service(domain = 4302, op = 261, request = RequestGetGamePropList.class, response = ResponseGetGamePropList.class)
	@Return(resultType = ResponseGetGamePropList.class)
	Result<ResponseGetGamePropList> getGamePropList(@Attribute(name = "param") GetGamePropListParam param);
	
	
	/**
	 *  获取用户背包状态
	 *
	 * @param param
	 *            
	 * @return 
	 *     //if rcode == 0 查询成功<br>
	 *     //if rcode == 1 参数错误<br>
	 *     //if rcode == 2 查询失败<br>
	 */
	@Service(domain = 4302, op = 262, request = RequestGetUserPropStatus.class, response = ResponseGetUserPropStatus.class)
	@Return(resultType = ResponseGetUserPropStatus.class)
	Result<ResponseGetUserPropStatus> getUserPropStatus(@Attribute(name = "param") GetUserPropStatusParam param);
	
	
	/**
	 *  查询道具发放状态
	 *
	 * @param param
	 *            
	 * @return 
	 *     //if rcode == 0 查询成功<br>
	 *     //if rcode == 1 参数错误<br>
	 *     //if rcode == 2 记录不存在<br>
	 *     //if rcode == 3 查询失败<br>
	 */
	@Service(domain = 4302, op = 263, request = RequestQueryPropGrantStatus.class, response = ResponseQueryPropGrantStatus.class)
	@Return(resultType = ResponseQueryPropGrantStatus.class)
	Result<ResponseQueryPropGrantStatus> queryPropGrantStatus(@Attribute(name = "param") QueryPropGrantStatusParam param);
	
	
	public static final int GRANT_GAME_PROP_SUCCESS = 0; // 发放成功
	public static final int GRANT_GAME_PROP_PARAM_ERROR = 1; // 参数错误
	public static final int GRANT_GAME_PROP_NOT_EXISTS = 2; // 道具不存在
	public static final int GRANT_GAME_PROP_USER_NOT_EXISTS = 3; // 用户不存在
	public static final int GRANT_GAME_PROP_FAIL = 4; // 发放失败
	public static final int GRANT_GAME_PROP_ERROR = 5; // 内部错误

	public static final int GET_GAME_PROP_LIST_SUCCESS = 0; // 查询成功
	public static final int GET_GAME_PROP_LIST_PARAM_ERROR = 1; // 参数错误
	public static final int GET_GAME_PROP_LIST_ERROR = 2; // 内部错误

	public static final int GET_USER_PROP_STATUS_SUCCESS = 0; // 查询成功
	public static final int GET_USER_PROP_STATUS_PARAM_ERROR = 1; // 参数错误
	public static final int GET_USER_PROP_STATUS_ERROR = 2; // 查询失败

	public static final int QUERY_PROP_GRANT_STATUS_SUCCESS = 0; // 查询成功
	public static final int QUERY_PROP_GRANT_STATUS_PARAM_ERROR = 1; // 参数错误
	public static final int QUERY_PROP_GRANT_STATUS_NOT_FOUND = 2; // 记录不存在
	public static final int QUERY_PROP_GRANT_STATUS_ERROR = 3; // 查询失败


}