package fm.lizhi.ocean.seal.api.impl;

import cn.hutool.core.util.StrUtil;
import com.google.inject.Inject;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.api.GamePropService;
import fm.lizhi.ocean.seal.constant.GamePropGrantStatus;
import fm.lizhi.ocean.seal.convert.GamePropServiceConvert;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GamePropManager;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetGamePropList;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGrantGameProp;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetUserPropStatusParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.QueryPropGrantStatusParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseGetUserPropStatus;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.ResponseQueryPropGrantStatus;
import fm.lizhi.ocean.seal.convert.GamePropQueryServiceConvert;
import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 游戏道具服务实现类
 * <AUTHOR>
 */
@Slf4j
@ServiceProvider
public class GamePropServiceImpl implements GamePropService {

    @Inject
    private GamePropManager gamePropManager;


    @Override
    public Result<ResponseGrantGameProp> grantGameProp(GrantGamePropParam param) {

        try {
            // 参数校验
            if (param == null) {
                return new Result<>(GRANT_GAME_PROP_PARAM_ERROR, null);
            }

            if (param.getUserId() <= 0 || StrUtil.isEmpty(param.getChannelPropId())  || param.getNum() <= 0) {
                log.warn("Invalid parameters: userId={}, propId={}, num={}",
                           param.getUserId(), param.getChannelPropId(), param.getNum());
                return new Result<>(GRANT_GAME_PROP_PARAM_ERROR, null);
            }

            // 调用业务逻辑
            Long flowId = gamePropManager.grantGameProp(param);

            // 构建响应
            ResponseGrantGameProp response = ResponseGrantGameProp.newBuilder()
                .setGrantStatus(GamePropGrantStatus.SUCCESS.getCode())
                .setMessage("Grant success")
                .setFlowId(flowId)
                .build();

            return new Result<>(GRANT_GAME_PROP_SUCCESS, response);

        } catch (IllegalArgumentException e) {
            log.warn("Grant game prop parameter error: {}", e.getMessage(), e);
            return new Result<>(GRANT_GAME_PROP_PARAM_ERROR, null);
        } catch (RuntimeException e) {
            log.error("Grant game prop failed: {}", e.getMessage(), e);
            ResponseGrantGameProp response = ResponseGrantGameProp.newBuilder()
                .setGrantStatus(GamePropGrantStatus.FAILED.getCode())
                .setMessage(e.getMessage())
                .build();
            return new Result<>(GRANT_GAME_PROP_FAIL, response);
        } catch (Exception e) {
            log.error("Grant game prop error", e);
            return new Result<>(GRANT_GAME_PROP_ERROR, null);
        }
    }

    @Override
    public Result<ResponseGetGamePropList> getGamePropList(GetGamePropListParam param) {
        log.info("Get game prop list, param: {}", param);

        try {
            // 参数校验
            if (param == null) {
                return new Result<>(GET_GAME_PROP_LIST_PARAM_ERROR, null);
            }

            // 查询道具列表
            PageList<GamePropBean> propList = gamePropManager.getGamePropList(param);

            // 转换为协议对象
            ResponseGetGamePropList.Builder responseBuilder = ResponseGetGamePropList.newBuilder();
            responseBuilder.setTotal(propList.getTotal());
            responseBuilder.setPageNumber(propList.getNextPageNumber());
            responseBuilder.setPageSize(propList.getPageSize());
            responseBuilder.addAllGameProps(GamePropServiceConvert.I.convertGamePropBeansToGameProps(propList));

            return new Result<>(GET_GAME_PROP_LIST_SUCCESS, responseBuilder.build());

        } catch (Exception e) {
            log.error("Get game prop list error", e);
            return new Result<>(GET_GAME_PROP_LIST_ERROR, null);
        }
    }

    @Override
    public Result<ResponseGetUserPropStatus> getUserPropStatus(GetUserPropStatusParam param) {
        log.info("Get user prop status, param: {}", param);

        try {
            // 参数校验
            if (param == null) {
                return new Result<>(GET_USER_PROP_STATUS_PARAM_ERROR, null);
            }

            if (StrUtil.isEmpty(param.getUserId()) || StrUtil.isEmpty(param.getChannelGameId()) ||
                StrUtil.isEmpty(param.getChannel()) || StrUtil.isEmpty(param.getAppId())) {
                log.warn("Invalid parameters: userId={}, gameId={}, channel={}, appId={}",
                           param.getUserId(), param.getChannelGameId(), param.getChannel(), param.getAppId());
                return new Result<>(GET_USER_PROP_STATUS_PARAM_ERROR, null);
            }

            // 调用业务逻辑
            UserPropStatusResult queryResult = gamePropManager.getUserPropStatus(param);

            // 构建响应
            ResponseGetUserPropStatus.Builder responseBuilder = ResponseGetUserPropStatus.newBuilder();
            if (queryResult.getUserProps() != null) {
                responseBuilder.addAllProps(GamePropQueryServiceConvert.INSTANCE.convertUserPropStatusInfoListToProtoList(queryResult.getUserProps()));
            }

            return new Result<>(GET_USER_PROP_STATUS_SUCCESS, responseBuilder.build());

        } catch (IllegalArgumentException e) {
            log.warn("Get user prop status parameter error: {}", e.getMessage(), e);
            return new Result<>(GET_USER_PROP_STATUS_PARAM_ERROR, null);
        } catch (RuntimeException e) {
            log.warn("Get user prop status business error: {}", e.getMessage(), e);
            return new Result<>(GET_USER_PROP_STATUS_ERROR, null);
        } catch (Exception e) {
            log.error("Get user prop status error", e);
            return new Result<>(GET_USER_PROP_STATUS_ERROR, null);
        }
    }

    @Override
    public Result<ResponseQueryPropGrantStatus> queryPropGrantStatus(QueryPropGrantStatusParam param) {
        log.info("Query prop grant status, param: {}", param);

        try {
            // 参数校验
            if (param == null) {
                return new Result<>(QUERY_PROP_GRANT_STATUS_PARAM_ERROR, null);
            }

            if (StrUtil.isEmpty(param.getUniqueId()) || StrUtil.isEmpty(param.getChannelGameId()) ||
                StrUtil.isEmpty(param.getChannel()) || StrUtil.isEmpty(param.getAppId())) {
                log.warn("Invalid parameters: uniqueId={}, channelGameId={}, channel={}, appId={}",
                           param.getUniqueId(), param.getChannelGameId(), param.getChannel(), param.getAppId());
                return new Result<>(QUERY_PROP_GRANT_STATUS_PARAM_ERROR, null);
            }

            // 调用业务逻辑
            PropGrantStatusResult queryResult = gamePropManager.queryPropGrantStatus(param);

            // 构建响应
            ResponseQueryPropGrantStatus.Builder responseBuilder = ResponseQueryPropGrantStatus.newBuilder();
            if (queryResult.getPropGrantStatus() != null) {
                responseBuilder.setGrantInfo(GamePropQueryServiceConvert.INSTANCE.convertPropGrantStatusInfoToProto(queryResult.getPropGrantStatus(), param));
            }

            return new Result<>(QUERY_PROP_GRANT_STATUS_SUCCESS, responseBuilder.build());

        } catch (IllegalArgumentException e) {
            log.warn("Query prop grant status parameter error: {}", e.getMessage(), e);
            return new Result<>(QUERY_PROP_GRANT_STATUS_PARAM_ERROR, null);
        } catch (RuntimeException e) {
            log.warn("Query prop grant status business error: {}", e.getMessage(), e);
            return new Result<>(QUERY_PROP_GRANT_STATUS_ERROR, null);
        } catch (Exception e) {
            log.error("Query prop grant status error", e);
            return new Result<>(QUERY_PROP_GRANT_STATUS_ERROR, null);
        }
    }

}
