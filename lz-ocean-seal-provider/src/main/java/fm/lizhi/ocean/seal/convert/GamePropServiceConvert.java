package fm.lizhi.ocean.seal.convert;


import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.seal.constant.GamePropGrantStatus;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.stream.Collectors;

/**
 * 游戏道具服务转换器
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {GamePropGrantStatus.class, Date.class},
        uses = {CommonConvert.class, ProtocolConvert.class}
)
public interface GamePropServiceConvert {

    GamePropServiceConvert I = Mappers.getMapper(GamePropServiceConvert.class);

    default Iterable<GamePropServiceProto.GameProp> convertGamePropBeansToGameProps(PageList<GamePropBean> propList){
        return propList.stream().map(this::convertGamePropBeanToGameProp).collect(Collectors.toList());
    }

    @ProtocolConvert.ProtocolCompleteMapping
    GamePropServiceProto.GameProp convertGamePropBeanToGameProp(GamePropBean gamePropBean);

    @Mapping(target = "propId", source = "gameProp.id")
    @Mapping(target = "grantStatus", expression = "java(GamePropGrantStatus.NOT_GRANTED.getCode())")
    @Mapping(target = "channelPropId", source = "gameProp.channelPropId")
    @Mapping(target = "appId", source = "gameProp.appId")
    @Mapping(target = "createTime", expression = "java(new Date())")
    @Mapping(target = "modifyTime", expression = "java(new Date())")
    GamePropFlowBean createPropFlowBean(GamePropServiceProto.GrantGamePropParam param, GamePropBean gameProp);
}
