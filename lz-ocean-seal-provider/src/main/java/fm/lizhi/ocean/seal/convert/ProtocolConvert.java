package fm.lizhi.ocean.seal.convert;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Protocol专用转换器基类
 * 提供Protocol生成类的通用字段忽略映射，避免在每个Convert类中重复定义大量的@Mapping(target = "xxx", ignore = true)
 * 
 * <p>Protocol生成的类通常包含以下类型的无效字段：</p>
 * <ul>
 *   <li>xxxBytes字段：如channelPropIdBytes、nameBytes、propDescBytes等</li>
 *   <li>protobuf内部字段：如unknownFields、defaultInstanceForType、allFields</li>
 *   <li>protobuf方法字段：如mergeFrom、mergeUnknownFields、clearField</li>
 * </ul>
 * 
 * <p>使用示例：</p>
 * <pre>{@code
 * @Mapper(uses = {CommonConvert.class, ProtocolConvert.class})
 * public interface MyProtocolConvert {
 *     @ProtocolMapping
 *     GamePropServiceProto.GameProp convertToProto(GamePropBean bean);
 * }
 * }</pre>
 * 
 * <AUTHOR>
 */
@Mapper
public interface ProtocolConvert {

    /**
     * 通用的Protocol字段忽略映射注解
     * 包含所有Protocol生成类的常见无效字段
     */
    @Mapping(target = "unknownFields", ignore = true)
    @Mapping(target = "defaultInstanceForType", ignore = true)
    @Mapping(target = "allFields", ignore = true)
    @Mapping(target = "mergeFrom", ignore = true)
    @Mapping(target = "mergeUnknownFields", ignore = true)
    @Mapping(target = "clearField", ignore = true)
    @Mapping(target = "serializedSize", ignore = true)
    @Mapping(target = "parserForType", ignore = true)
    @Mapping(target = "descriptorForType", ignore = true)
    @Mapping(target = "initializationErrorString", ignore = true)
    @Mapping(target = "initialized", ignore = true)
    @interface ProtocolMapping {
    }

    /**
     * 包含字符串字段Bytes映射的Protocol字段忽略映射注解
     * 适用于包含字符串字段的Protocol类
     */
    @ProtocolMapping
    @Mapping(target = "channelPropIdBytes", ignore = true)
    @Mapping(target = "channelGameIdBytes", ignore = true)
    @Mapping(target = "nameBytes", ignore = true)
    @Mapping(target = "propDescBytes", ignore = true)
    @Mapping(target = "operatorBytes", ignore = true)
    @Mapping(target = "remarkBytes", ignore = true)
    @Mapping(target = "iconUrlBytes", ignore = true)
    @Mapping(target = "propIdBytes", ignore = true)
    @Mapping(target = "eventNameBytes", ignore = true)
    @Mapping(target = "channelBytes", ignore = true)
    @Mapping(target = "appIdBytes", ignore = true)
    @Mapping(target = "gameIdBytes", ignore = true)
    @Mapping(target = "dataJsonBytes", ignore = true)
    @Mapping(target = "roomIdBytes", ignore = true)
    @Mapping(target = "msgBytes", ignore = true)
    @Mapping(target = "dataBytes", ignore = true)
    @Mapping(target = "userIdBytes", ignore = true)
    @Mapping(target = "uniqueIdBytes", ignore = true)
    @interface ProtocolStringMapping {
    }

    /**
     * 包含数字字段相关映射的Protocol字段忽略映射注解
     * 适用于包含数字字段的Protocol类
     */
    @ProtocolMapping
    @Mapping(target = "memoizedHashCode", ignore = true)
    @Mapping(target = "memoizedSize", ignore = true)
    @interface ProtocolNumberMapping {
    }

    /**
     * 最完整的Protocol字段忽略映射注解
     * 包含所有常见的Protocol无效字段
     */
    @ProtocolStringMapping
    @ProtocolNumberMapping
    @interface ProtocolCompleteMapping {
    }
}
