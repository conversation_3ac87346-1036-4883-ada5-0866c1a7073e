package fm.lizhi.ocean.seal.manager;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.common.datastore.mysql.annotation.Transactional;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.seal.convert.GamePropServiceConvert;
import fm.lizhi.ocean.seal.dao.bean.*;
import fm.lizhi.ocean.seal.dao.GamePropDao;
import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetUserPropStatusParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.QueryPropGrantStatusParam;
import fm.lizhi.ocean.seal.constant.GamePropGrantStatus;
import fm.lizhi.ocean.seal.strategy.factory.GamePropStrategyFactory;
import fm.lizhi.ocean.seal.strategy.GamePropStrategy;
import fm.lizhi.ocean.seal.pojo.GamePropGrantResult;


import lombok.extern.slf4j.Slf4j;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * 游戏道具业务管理类
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GamePropManager {

    @Inject
    private GamePropDao gamePropDao;

    @Inject
    private GamePropGrantStrategyFactory gamePropGrantStrategyFactory;

    @Inject
    private GamePropQueryStrategyFactory gamePropQueryStrategyFactory;

    /**
     * 道具发放
     * @param param 发放参数
     * @return 流水ID
     */
    public Long grantGameProp(GrantGamePropParam param) {
        log.info("Grant game prop, param: {}", param);

        // 1. 参数校验

        if (param == null || param.getUserId() <= 0 || StrUtil.isEmpty(param.getChannelPropId()) ||
            param.getNum() <= 0 || StringUtils.isEmpty(param.getUniqueId())) {
            throw new IllegalArgumentException("Invalid parameters");
        }

        String channelPropId = param.getChannelPropId();

        // 2. 幂等性检查
        GamePropFlowBean existingFlow = gamePropDao.selectFlowByUniqueId(param.getUniqueId(), GamePropGrantStatus.SUCCESS.getCode());
        if (existingFlow != null) {
            log.info("Duplicate grant request, uniqueId: {}, flowId: {}",
                       param.getUniqueId(), existingFlow.getId());
            return existingFlow.getId();
        }

        // 3. 查询道具信息
        GamePropBean gameProp = gamePropDao.selectOne(channelPropId, param.getAppId(), param.getGameId());
        if (gameProp == null) {
            throw new IllegalArgumentException("Game prop not exists or deleted, propId: " + channelPropId);
        }

        // 4. 创建流水记录（状态为发放中）
        GamePropFlowBean flowBean = GamePropServiceConvert.I.createPropFlowBean(param, gameProp);
        flowBean.setGrantStatus(GamePropGrantStatus.GRANTING.getCode());
        gamePropDao.insertFlow(flowBean);
        Long flowId = flowBean.getId();

        // 5. 调用游戏厂商发放接口
        GamePropGrantResult grantResult = invokeGamePropGrantProp(param, gameProp);

        // 6. 根据调用结果更新流水状态
        if (grantResult.isSuccess()) {
            gamePropDao.updateFlowGrantStatus(flowId, GamePropGrantStatus.SUCCESS.getCode());
            log.info("Grant game prop success, flowId: {}, result: {}", flowId, grantResult);
        } else {
            gamePropDao.updateFlowGrantStatus(flowId, GamePropGrantStatus.FAILED.getCode());
            log.warn("Grant game prop failed, flowId: {}, result: {}", flowId, grantResult);
            throw new RuntimeException("Game prop grant failed: " + grantResult.getMessage());
        }

        return flowId;
    }

    /**
     * 查询道具列表
     * @param param 查询参数
     * @return 道具列表
     */
    public PageList<GamePropBean> getGamePropList(GetGamePropListParam param) {

        return gamePropDao.selectPropListByConditions(
            param.getAppId(),
            param.getChannelGameId(),
            param.getType(),
            param.getPageNumber(),
            param.getPageSize()
        );
    }


    /**
     * 获取用户背包状态
     * @param param 查询参数
     * @return 查询结果
     */
    public UserPropStatusResult getUserPropStatus(GetUserPropStatusParam param) {

        // 获取策略并执行查询
        GamePropQueryStrategy strategy = gamePropQueryStrategyFactory.getStrategy(param.getChannel());
        return strategy.getUserPropStatus(param);
    }

    /**
     * 查询道具发放状态
     * @param param 查询参数
     * @return 查询结果
     */
    public PropGrantStatusResult queryPropGrantStatus(QueryPropGrantStatusParam param) {

        // 获取策略并执行查询
        GamePropQueryStrategy strategy = gamePropQueryStrategyFactory.getStrategy(param.getChannel());
        return strategy.queryPropGrantStatus(param);
    }

    /**
     * 调用游戏厂商道具发放接口
     */
    private GamePropGrantResult invokeGamePropGrantProp(GrantGamePropParam param, GamePropBean gameProp) {
        // 获取策略并执行发放
        GamePropGrantStrategy strategy = gamePropGrantStrategyFactory.getStrategy(param.getChannel());
        return strategy.grantProp(param, gameProp);

    }
}
