package fm.lizhi.ocean.seal.strategy;

import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.pojo.GamePropGrantResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetUserPropStatusParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.QueryPropGrantStatusParam;

/**
 * 游戏道具策略接口（合并发放和查询功能）
 * <AUTHOR>
 */
public interface GamePropStrategy {

    // ==================== 道具发放相关方法 ====================

    /**
     * 发放道具
     * @param param 发放参数
     * @param gameProp 道具信息
     * @param bizGameBean 业务游戏信息
     * @param gameInfoBean 游戏信息
     * @param gameChannelBean 渠道信息
     * @return 发放结果
     */
    GamePropGrantResult grantProp(GrantGamePropParam param,
                                  GamePropBean gameProp,
                                  GameBizGameBean bizGameBean,
                                  GameInfoBean gameInfoBean,
                                  GameChannelBean gameChannelBean);

    /**
     * 发放道具（策略内部处理数据查询）
     * @param param 发放参数
     * @param gameProp 道具信息
     * @return 发放结果
     */
    GamePropGrantResult grantProp(GrantGamePropParam param, GamePropBean gameProp);

    // ==================== 道具查询相关方法 ====================

    /**
     * 获取用户背包状态
     * @param param 查询参数
     * @return 查询结果
     */
    UserPropStatusResult getUserPropStatus(GetUserPropStatusParam param);

    /**
     * 查询道具发放状态
     * @param param 查询参数
     * @return 查询结果
     */
    PropGrantStatusResult queryPropGrantStatus(QueryPropGrantStatusParam param);

    // ==================== 通用方法 ====================

    /**
     * 是否支持该渠道
     * @param channel 渠道名称
     * @return 是否支持
     */
    boolean supports(String channel);

    // ==================== 常量定义 ====================

    int GRANT_SUCCESS = 0;
    int GRANT_FAIL = 1;
}
