package fm.lizhi.ocean.seal.strategy.factory;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.strategy.GamePropStrategy;
import fm.lizhi.ocean.seal.strategy.impl.LukGamePropStrategy;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 游戏道具策略工厂（合并发放和查询策略）
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GamePropStrategyFactory {

    @Inject
    private LukGamePropStrategy lukGamePropStrategy;

    /**
     * 根据渠道获取策略
     * @param channel 渠道名称
     * @return 策略实例
     */
    public GamePropStrategy getStrategy(String channel) {
        log.debug("Get game prop strategy for channel: {}", channel);
        
        for (GamePropStrategy strategy : getSupportedStrategies()) {
            if (strategy.supports(channel)) {
                log.debug("Found strategy: {} for channel: {}", strategy.getClass().getSimpleName(), channel);
                return strategy;
            }
        }
        
        log.warn("No strategy found for channel: {}", channel);
        throw new IllegalArgumentException("No strategy found for channel: " + channel);
    }

    /**
     * 获取所有支持的策略列表
     *
     * @return 支持的策略列表
     */
    public List<GamePropStrategy> getSupportedStrategies() {
        return Arrays.asList(lukGamePropStrategy);
    }
}
