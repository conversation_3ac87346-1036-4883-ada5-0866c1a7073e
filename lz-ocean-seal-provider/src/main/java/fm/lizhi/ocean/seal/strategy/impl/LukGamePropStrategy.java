package fm.lizhi.ocean.seal.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.LukControlEventType;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.manager.BizGameManager;
import fm.lizhi.ocean.seal.manager.GameChannelManager;
import fm.lizhi.ocean.seal.manager.GameInfoManager;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.pojo.GamePropGrantResult;
import fm.lizhi.ocean.seal.pojo.PropGrantStatusResult;
import fm.lizhi.ocean.seal.pojo.UserPropStatusResult;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetUserPropStatusParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.QueryPropGrantStatusParam;
import fm.lizhi.ocean.seal.strategy.GamePropStrategy;
import io.github.cfgametech.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LUK 渠道游戏道具策略实现（合并发放和查询功能）
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class LukGamePropStrategy implements GamePropStrategy {

    @Inject
    private LukManger lukManager;

    @Inject
    private BizGameManager bizGameManager;
    @Inject
    private GameChannelManager gameChannelManager;
    @Inject
    private GameInfoManager gameInfoManager;

    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }

    // ==================== 道具发放相关方法 ====================

    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param,
                                         GamePropBean gameProp,
                                         GameBizGameBean bizGameBean,
                                         GameInfoBean gameInfoBean,
                                         GameChannelBean gameChannelBean) {
        
        log.info("LUK grant prop, gameId: {}, propId: {}, userId: {}", bizGameBean.getId(), gameProp.getId(), param.getUserId());

        try {
            // 构建道具发放数据
            Map<String, Object> grantPropData = buildGrantPropData(param, gameProp);

            // 调用通用的控制事件发布方法
            Response<?> sdkResult = lukManager.publishGlobalEvent(
                    param.getAppId(),
                    String.valueOf(gameInfoBean.getChannelGameId()),
                    LukControlEventType.GRANT_PROP,
                    grantPropData
            );

            // 转换结果
            return convertLukResult(sdkResult);
            
        } catch (Exception e) {
            log.error("LUK grant prop failed, gameId: {}, propId: {}, userId: {}", 
                     bizGameBean.getId(), gameProp.getId(), param.getUserId(), e);
            return GamePropGrantResult.failure(GRANT_FAIL, "LUK grant prop failed: " + e.getMessage());
        }
    }

    @Override
    public GamePropGrantResult grantProp(GrantGamePropParam param, GamePropBean gameProp) {
        // 查询gameId是属于游戏渠道的gameId，还是属于seal平台biz_gameId
        GameBizGameBean bizGameBean = bizGameManager.getGame(param.getAppId(), param.getGameId());
        if (bizGameBean == null) {
            throw new IllegalArgumentException("gameId not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 获取游戏信息
        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoBeanById(bizGameBean.getGameInfoId());
        if (gameInfoBean == null) {
            throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 获取渠道信息
        GameChannelBean gameChannelBean = gameChannelManager.getGameChannel(bizGameBean.getChannelId());
        if (gameChannelBean == null) {
            throw new IllegalArgumentException("channel not exist. appId=" + param.getAppId() + ", gameId=" + param.getGameId());
        }

        // 调用原有的发放方法
        return grantProp(param, gameProp, bizGameBean, gameInfoBean, gameChannelBean);
    }

    // ==================== 道具查询相关方法 ====================

    @Override
    public UserPropStatusResult getUserPropStatus(GetUserPropStatusParam param) {
        log.info("LUK get user prop status, gameId: {}, userId: {}", param.getChannelGameId(), param.getUserId());

        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoPO(param.getChannel(), Long.parseLong(param.getChannelGameId()));
        if (gameInfoBean == null) {
            throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", gameId=" + param.getChannelGameId());
        }

        // 构建获取背包状态的数据
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("user_id", param.getUserId());

        // 调用 LUK SDK
        Response<?> sdkResult = lukManager.publishGlobalEvent(
                param.getAppId(),
                String.valueOf(gameInfoBean.getChannelGameId()),
                LukControlEventType.GET_USER_PROPS,
                eventData
        );

        if (sdkResult.suc()) {
            log.info("LUK get user props success, data: {}", sdkResult.getData());
        } else {
            log.warn("LUK get user props failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
        }
        // 转换结果
        return convertLukUserPropsResult(sdkResult, param);
    }

    @Override
    public PropGrantStatusResult queryPropGrantStatus(QueryPropGrantStatusParam param) {

        GameInfoBean gameInfoBean = gameInfoManager.getGameInfoPO(param.getChannel(), Long.parseLong(param.getChannelGameId()));
        if (gameInfoBean == null) {
            throw new IllegalArgumentException("gameInfo not exist. appId=" + param.getAppId() + ", channelGameId=" + param.getChannelGameId());
        }

        // 构建查询道具发放状态的数据
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("unique_id", param.getUniqueId());

        // 调用 LUK SDK
        Response<?> sdkResult = lukManager.publishGlobalEvent(
                param.getAppId(),
                String.valueOf(gameInfoBean.getChannelGameId()),
                LukControlEventType.QUERY_PROP_STATUS,
                eventData
        );
        if (sdkResult.suc()) {
            log.info("LUK query prop status success, data: {}", sdkResult.getData());
        } else {
            log.warn("LUK query prop status failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
        }

        // 转换结果
        return convertLukPropStatusResult(sdkResult, param);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建道具发放数据
     */
    private Map<String, Object> buildGrantPropData(GrantGamePropParam param, GamePropBean gameProp) {
        Map<String, Object> grantPropData = new HashMap<>();
        grantPropData.put("user_id", param.getUserId());
        grantPropData.put("prop_id", gameProp.getChannelPropId());
        grantPropData.put("num", param.getNum());
        grantPropData.put("unique_id", param.getUniqueId());
        grantPropData.put("extra", param.getExtra());
        return grantPropData;
    }

    /**
     * 转换 LUK SDK 发放结果
     */
    private GamePropGrantResult convertLukResult(Response<?> sdkResult) {
        if (sdkResult.suc()) {
            log.info("LUK grant prop success, data: {}", sdkResult.getData());
            return GamePropGrantResult.success(sdkResult.getData() != null ? sdkResult.getData().toString() : null);
        } else {
            log.warn("LUK grant prop failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
            return GamePropGrantResult.failure(sdkResult.getCode(), sdkResult.getMessage());
        }
    }

    /**
     * 转换 LUK SDK 用户道具查询结果
     */
    private UserPropStatusResult convertLukUserPropsResult(Response<?> sdkResult, GetUserPropStatusParam param) {
        if (!sdkResult.suc()) {
            log.warn("LUK get user props failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
            return UserPropStatusResult.failure();
        }

        Object data = sdkResult.getData();
        if (data == null) {
            log.warn("LUK get user props success but data is null");
            return UserPropStatusResult.success(new ArrayList<>());
        }

        JSONObject dataJson = (JSONObject) JSONObject.toJSON(data);
        List<Object> propsArray = dataJson.getJSONArray("props");

        List<UserPropStatusResult.UserPropStatusInfo> userProps = new ArrayList<>();
        if (propsArray != null) {
            for (Object propObj : propsArray) {
                JSONObject prop = (JSONObject) JSONObject.toJSON(propObj);
                UserPropStatusResult.UserPropStatusInfo userProp = new UserPropStatusResult.UserPropStatusInfo()
                        .setPropId(prop.getString("prop_id"))
                        .setType(prop.getInteger("type"))
                        .setNum(prop.getInteger("num"))
                        .setExpireTime(prop.getLong("expire_time"));
                userProps.add(userProp);
            }
        }

        return UserPropStatusResult.success(userProps);
    }

    /**
     * 转换 LUK SDK 道具发放状态查询结果
     */
    private PropGrantStatusResult convertLukPropStatusResult(Response<?> sdkResult, QueryPropGrantStatusParam param) {
        if (!sdkResult.suc()) {
            log.warn("LUK query prop status failed, code: {}, message: {}", sdkResult.getCode(), sdkResult.getMessage());
            return PropGrantStatusResult.failure();
        }

        Object data = sdkResult.getData();
        if (data == null) {
            log.warn("LUK query prop status success but data is null");
            return PropGrantStatusResult.failure();
        }

        JSONObject dataJson = (JSONObject) JSONObject.toJSON(data);

        // 解析道具详情
        List<PropGrantStatusResult.PropGrantStatusInfo.PropDetail> details = new ArrayList<>();
        List<Object> detailsArray = dataJson.getJSONArray("details");
        if (detailsArray != null) {
            for (Object detailObj : detailsArray) {
                JSONObject detail = (JSONObject) JSONObject.toJSON(detailObj);
                PropGrantStatusResult.PropGrantStatusInfo.PropDetail propDetail = 
                    new PropGrantStatusResult.PropGrantStatusInfo.PropDetail()
                        .setPropId(detail.getString("prop_id"))
                        .setNum(detail.getInteger("num"))
                        .setStatus(detail.getInteger("status"));
                details.add(propDetail);
            }
        }

        // 构建发放状态信息
        PropGrantStatusResult.PropGrantStatusInfo statusInfo = new PropGrantStatusResult.PropGrantStatusInfo()
                .setAppId(param.getAppId())
                .setGameId(param.getChannelGameId())
                .setUniqueId(dataJson.getString("unique_id"))
                .setUserId(dataJson.getString("user_id"))
                .setDetails(details)
                .setExtra(dataJson.getString("extra"))
                .setStatus(dataJson.getInteger("status"))
                .setCreatedTime(dataJson.getLong("created_time"));

        return PropGrantStatusResult.success(statusInfo);
    }
}
