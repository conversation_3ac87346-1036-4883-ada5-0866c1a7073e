<configuration>

    <appender name="infoAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <file>${LOG_PATH}/info.log</file>
	    <append>true</append>
	    <encoder>
	            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}`CID=%X{cid:-N/A}`MID=%X{mid:-N/A}`TID=%X{tid:-N/A}`SID=%X{sid:-N/A}`%m%n</Pattern>
	    </encoder>
	    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	            <fileNamePattern>${LOG_PATH}/info.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
	    </rollingPolicy>
    </appender>
    <appender name="asynInfoAppender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>10000</queueSize>
        <appender-ref ref="infoAppender" />
    </appender>

    <appender name="accessAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <file>${LOG_PATH}/access.log</file>
	    <append>true</append>
	    <encoder>
	            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - `CID=%X{cid:-N/A}`MID=%X{mid:-N/A}`TID=%X{tid:-N/A}`SID=%X{sid:-N/A}`%msg%n</Pattern>
	    </encoder>
	    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	            <fileNamePattern>${LOG_PATH}/access.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
	    </rollingPolicy>
    </appender>
    <appender name="asynAccessAppender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>10000</queueSize>
        <appender-ref ref="accessAppender" />
    </appender>

    <appender name="async-connector" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/async-connector.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/async-connector.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name="asyncConnectorAppender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>10000</queueSize>
        <appender-ref ref="async-connector" />
    </appender>

    <appender name="cache_stat_adp" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/cache_stat.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/cache_stat.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
        </rollingPolicy>
    </appender>
    <appender name="asyncCacheStatAppender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>10000</queueSize>
        <appender-ref ref="cache_stat_adp" />
    </appender>

    <appender name="serverAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
	    <file>${LOG_PATH}/server.log</file>
	    <append>true</append>
	    <encoder>
	            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - `CID=%X{cid:-N/A}`MID=%X{mid:-N/A}`TID=%X{tid:-N/A}`SID=%X{sid:-N/A}`%msg%n</Pattern>
	    </encoder>
	    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
	            <fileNamePattern>${LOG_PATH}/server.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
	    </rollingPolicy>
    </appender>
    <appender name="asyncServerAppender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>10000</queueSize>
        <appender-ref ref="serverAppender" />
    </appender>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="access_log" level="info" additivity="false">
        <appender-ref ref="asynAccessAppender" />
    </logger>

    <logger name="info" level="info" additivity="false">
        <appender-ref ref="asynInfoAppender" />
    </logger>

    <logger name="fm.lizhi.connector.async" level="info" additivity="false">
        <appender-ref ref="asyncConnectorAppender" />
    </logger>

    <logger name="cache_stat" level="info" additivity="true">
        <appender-ref ref="asyncCacheStatAppender" />
    </logger>

    <logger name="lizhi" level="info" additivity="false">
        <appender-ref ref="asynInfoAppender" />
    </logger>

    <appender name="catAppender" class="com.dianping.cat.logback.CatLogbackAppender"/>
    <root level="info">
        <appender-ref ref="asyncServerAppender"/>
        <appender-ref ref="stdout"/>
        <appender-ref ref="catAppender" />
    </root>

    <appender name="errorAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%X{traceId}] - %msg%n
            </pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error.%d{yyyy-MM-dd_HH}.log</fileNamePattern>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>
    <appender name="asyncErrorAppender" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>4096</queueSize>
        <appender-ref ref="errorAppender"/>
    </appender>

</configuration>